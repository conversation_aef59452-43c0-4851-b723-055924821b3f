"""Batch processing for photo centering."""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import Config
from ..models.unified_detector import UnifiedHumanDetector
from ..image_processing.raw_processor import RawProcessor
from ..image_processing.centering import PhotoCenterer
from ..image_processing.crop_centering import CropCenterer


@dataclass
class BatchResult:
    """Result of batch processing operation."""
    total_files: int
    processed_files: int
    failed_files: int
    skipped_files: int
    processing_time: float
    results: List[Dict[str, Any]]


@dataclass
class FileProcessingResult:
    """Result of processing a single file."""
    input_path: str
    output_path: str
    success: bool
    error_message: Optional[str] = None
    processing_time: float = 0.0
    detection_confidence: float = 0.0
    centering_confidence: float = 0.0
    # Additional fields for detailed logging
    original_dimensions: Optional[tuple] = None  # (width, height)
    output_dimensions: Optional[tuple] = None    # (width, height)
    model_used: Optional[str] = None
    centering_method: Optional[str] = None
    crop_ratio: Optional[float] = None


class BatchProcessor:
    """Batch processor for photo centering operations."""

    def __init__(self, config: Optional[Config] = None):
        """Initialize batch processor.

        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)

        # Initialize components
        self.human_detector = UnifiedHumanDetector(self.config)
        self.raw_processor = RawProcessor(self.config)
        self.photo_centerer = PhotoCenterer(self.config)
        self.crop_centerer = CropCenterer(self.config)

        # Setup batch logging
        self.batch_log_file = None

    def _setup_batch_logging(self) -> str:
        """Setup batch logging file.

        Returns:
            Path to the log file
        """
        # Create logs directory if it doesn't exist
        logs_dir = Path("./logs")
        logs_dir.mkdir(exist_ok=True)

        # Create timestamped log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"cropped_photos_{timestamp}.log"
        log_path = logs_dir / log_filename

        # Initialize log file with header
        with open(log_path, 'w', encoding='utf-8') as f:
            f.write(f"Photo Center Batch Processing Log\n")
            f.write(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")

        self.batch_log_file = str(log_path)
        return str(log_path)

    def _log_batch_entry(self, result: 'FileProcessingResult'):
        """Log a batch processing entry.

        Args:
            result: Processing result to log
        """
        if not self.batch_log_file:
            return

        try:
            with open(self.batch_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                if result.success:
                    f.write(f"[{timestamp}] SUCCESS\n")
                    f.write(f"  File: {result.input_path}\n")
                    f.write(f"  Original dimensions: {result.original_dimensions[0]}x{result.original_dimensions[1]}\n")
                    f.write(f"  Output dimensions: {result.output_dimensions[0]}x{result.output_dimensions[1]}\n")
                    f.write(f"  Model used: {result.model_used}\n")
                    f.write(f"  Centering method: {result.centering_method}\n")
                    f.write(f"  Crop ratio: {result.crop_ratio:.3f}\n")
                    f.write(f"  Detection confidence: {result.detection_confidence:.3f}\n")
                    f.write(f"  Centering confidence: {result.centering_confidence:.3f}\n")
                    f.write(f"  Processing time: {result.processing_time:.2f}s\n")
                    f.write(f"  Output: {result.output_path}\n")
                else:
                    f.write(f"[{timestamp}] FAILED\n")
                    f.write(f"  File: {result.input_path}\n")
                    f.write(f"  Error: {result.error_message}\n")
                    f.write(f"  Processing time: {result.processing_time:.2f}s\n")

                f.write("\n")
        except Exception as e:
            self.logger.warning(f"Failed to write to batch log: {e}")

    def process_directory(
        self,
        input_dir: str,
        output_dir: Optional[str] = None,
        recursive: bool = True,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> BatchResult:
        """Process all supported images in a directory.
        
        Args:
            input_dir: Input directory path
            output_dir: Output directory path. If None, creates subdirectory in input_dir
            recursive: Whether to process subdirectories recursively
            progress_callback: Optional callback function for progress updates (current, total)
            
        Returns:
            BatchResult with processing statistics
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")

        # Setup batch logging
        log_path = self._setup_batch_logging()
        self.logger.info(f"Batch processing log: {log_path}")

        # Setup output directory
        if output_dir is None:
            subdir_name = self.config.get('batch.subdirectory_name', 'centered')
            output_path = input_path / subdir_name
        else:
            output_path = Path(output_dir)

        output_path.mkdir(parents=True, exist_ok=True)
        
        # Find all supported image files
        image_files = self._find_image_files(input_path, recursive)
        
        if not image_files:
            self.logger.warning(f"No supported image files found in {input_dir}")
            return BatchResult(0, 0, 0, 0, 0.0, [])
        
        self.logger.info(f"Found {len(image_files)} image files to process")
        
        # Process files
        start_time = time.time()
        results = []
        
        max_workers = self.config.max_workers
        
        if max_workers > 1:
            # Parallel processing
            results = self._process_files_parallel(
                image_files, input_path, output_path, progress_callback
            )
        else:
            # Sequential processing
            results = self._process_files_sequential(
                image_files, input_path, output_path, progress_callback
            )
        
        processing_time = time.time() - start_time
        
        # Calculate statistics
        processed = sum(1 for r in results if r.success)
        failed = sum(1 for r in results if not r.success)
        skipped = len(image_files) - len(results)
        
        batch_result = BatchResult(
            total_files=len(image_files),
            processed_files=processed,
            failed_files=failed,
            skipped_files=skipped,
            processing_time=processing_time,
            results=[self._result_to_dict(r) for r in results]
        )
        
        self.logger.info(
            f"Batch processing completed: {processed} processed, "
            f"{failed} failed, {skipped} skipped in {processing_time:.2f}s"
        )

        # Finalize batch log
        if self.batch_log_file:
            try:
                with open(self.batch_log_file, 'a', encoding='utf-8') as f:
                    f.write("=" * 80 + "\n")
                    f.write(f"Batch processing completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Total files: {len(image_files)}\n")
                    f.write(f"Processed: {processed}\n")
                    f.write(f"Failed: {failed}\n")
                    f.write(f"Skipped: {skipped}\n")
                    f.write(f"Total time: {processing_time:.2f}s\n")
            except Exception as e:
                self.logger.warning(f"Failed to finalize batch log: {e}")

        return batch_result
    
    def _find_image_files(self, directory: Path, recursive: bool) -> List[Path]:
        """Find all supported image files in directory.
        
        Args:
            directory: Directory to search
            recursive: Whether to search recursively
            
        Returns:
            List of image file paths
        """
        supported_extensions = set(self.config.supported_extensions)
        image_files = []
        
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                image_files.append(file_path)
        
        return sorted(image_files)
    
    def _process_files_sequential(
        self,
        image_files: List[Path],
        input_dir: Path,
        output_dir: Path,
        progress_callback: Optional[Callable[[int, int], None]]
    ) -> List[FileProcessingResult]:
        """Process files sequentially.
        
        Args:
            image_files: List of image file paths
            input_dir: Input directory
            output_dir: Output directory
            progress_callback: Progress callback function
            
        Returns:
            List of processing results
        """
        results = []
        
        for i, file_path in enumerate(image_files):
            try:
                result = self._process_single_file(file_path, input_dir, output_dir)
                results.append(result)

                # Log the result
                self._log_batch_entry(result)

                if progress_callback:
                    progress_callback(i + 1, len(image_files))

            except Exception as e:
                self.logger.error(f"Error processing {file_path}: {e}")
                error_result = FileProcessingResult(
                    input_path=str(file_path),
                    output_path="",
                    success=False,
                    error_message=str(e)
                )
                results.append(error_result)
                self._log_batch_entry(error_result)
        
        return results

    def _process_files_parallel(
        self,
        image_files: List[Path],
        input_dir: Path,
        output_dir: Path,
        progress_callback: Optional[Callable[[int, int], None]]
    ) -> List[FileProcessingResult]:
        """Process files in parallel.

        Args:
            image_files: List of image file paths
            input_dir: Input directory
            output_dir: Output directory
            progress_callback: Progress callback function

        Returns:
            List of processing results
        """
        results = []
        completed_count = 0

        max_workers = self.config.max_workers

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(process_single_file_worker, str(file_path), str(input_dir), str(output_dir)): file_path
                for file_path in image_files
            }

            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                    # Log the result
                    self._log_batch_entry(result)
                except Exception as e:
                    self.logger.error(f"Error processing {file_path}: {e}")
                    error_result = FileProcessingResult(
                        input_path=str(file_path),
                        output_path="",
                        success=False,
                        error_message=str(e)
                    )
                    results.append(error_result)
                    self._log_batch_entry(error_result)

                completed_count += 1
                if progress_callback:
                    progress_callback(completed_count, len(image_files))

        return results

    def _process_single_file(
        self,
        file_path: Path,
        input_dir: Path,
        output_dir: Path
    ) -> FileProcessingResult:
        """Process a single image file using crop centering like the GUI.

        Args:
            file_path: Path to image file
            input_dir: Input directory
            output_dir: Output directory

        Returns:
            FileProcessingResult
        """
        start_time = time.time()

        try:
            # Load image
            image = self.raw_processor.load_image(file_path)
            bit_depth = self.raw_processor.get_bit_depth(image)
            self.logger.debug(f"Loaded image with {bit_depth}-bit depth: {file_path}")

            # Store original dimensions
            original_height, original_width = image.shape[:2]
            original_dimensions = (original_width, original_height)

            # Detect humans
            detections = self.human_detector.detect_humans(image)

            if not detections:
                return FileProcessingResult(
                    input_path=str(file_path),
                    output_path="",
                    success=False,
                    error_message="No human detected in image",
                    processing_time=time.time() - start_time,
                    original_dimensions=original_dimensions
                )

            # Get best detection (pass image shape for better center-person selection like GUI)
            image_shape = image.shape[:2]  # (height, width)
            best_detection = self.human_detector.get_best_detection(detections, image_shape)

            # Apply orientation correction if needed
            corrected_image, orientation_info = self.raw_processor.apply_orientation_correction(image, best_detection)
            if orientation_info['applied']:
                self.logger.info(f"Applied {orientation_info['rotation_angle']}° rotation (confidence: {orientation_info['confidence']:.3f}) to {file_path}")
                # Re-detect humans on corrected image for accurate centering
                corrected_detections = self.human_detector.detect_humans(corrected_image)
                if corrected_detections:
                    best_detection = self.human_detector.get_best_detection(corrected_detections, corrected_image.shape[:2])
                    image = corrected_image  # Use corrected image for centering
                else:
                    self.logger.warning(f"No humans detected after rotation for {file_path}, using original detection")
            elif orientation_info['needs_rotation']:
                self.logger.debug(f"Orientation correction suggested but confidence too low: {orientation_info['confidence']:.3f} for {file_path}")

            # Get the model name that was actually used (not "auto")
            model_used = self.human_detector.get_current_model_type()

            # Use crop centering like the GUI for better results
            crop_result = self.crop_centerer.center_subject_with_crop(
                image, best_detection, centering_method='face_chest_based'
            )

            # Generate output path
            relative_path = file_path.relative_to(input_dir)
            output_suffix = self.config.get('batch.output_suffix', '_centered')

            output_file_path = output_dir / relative_path.parent / f"{relative_path.stem}{output_suffix}{relative_path.suffix}"
            output_file_path.parent.mkdir(parents=True, exist_ok=True)

            # Save the cropped result (not padded preview)
            self.raw_processor.save_image(crop_result.cropped_image, output_file_path)

            # Get output dimensions
            output_height, output_width = crop_result.cropped_image.shape[:2]
            output_dimensions = (output_width, output_height)

            processing_time = time.time() - start_time

            return FileProcessingResult(
                input_path=str(file_path),
                output_path=str(output_file_path),
                success=True,
                processing_time=processing_time,
                detection_confidence=best_detection['confidence'],
                centering_confidence=crop_result.confidence,
                original_dimensions=original_dimensions,
                output_dimensions=output_dimensions,
                model_used=model_used,
                centering_method=crop_result.method_used,
                crop_ratio=crop_result.crop_ratio
            )

        except Exception as e:
            return FileProcessingResult(
                input_path=str(file_path),
                output_path="",
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time,
                original_dimensions=getattr(locals().get('image'), 'shape', (None, None))[:2] if 'image' in locals() else None
            )

    def _result_to_dict(self, result: FileProcessingResult) -> Dict[str, Any]:
        """Convert FileProcessingResult to dictionary.

        Args:
            result: FileProcessingResult to convert

        Returns:
            Dictionary representation
        """
        return {
            'input_path': result.input_path,
            'output_path': result.output_path,
            'success': result.success,
            'error_message': result.error_message,
            'processing_time': result.processing_time,
            'detection_confidence': result.detection_confidence,
            'centering_confidence': result.centering_confidence,
            'original_dimensions': result.original_dimensions,
            'output_dimensions': result.output_dimensions,
            'model_used': result.model_used,
            'centering_method': result.centering_method,
            'crop_ratio': result.crop_ratio
        }


def process_single_file_worker(file_path: str, input_dir: str, output_dir: str) -> FileProcessingResult:
    """Worker function for parallel processing.

    This function is used by ProcessPoolExecutor and needs to be at module level.

    Args:
        file_path: Path to image file
        input_dir: Input directory
        output_dir: Output directory

    Returns:
        FileProcessingResult
    """
    # Create new instances for this worker process
    config = Config()
    processor = BatchProcessor(config)

    return processor._process_single_file(
        Path(file_path),
        Path(input_dir),
        Path(output_dir)
    )
